.pricingPage {
  min-height: 100vh;
  background: #ffffff;
}

/* Navigation styles removed - using SharedHeader component */

/* Hero Section */
.heroSection {
  position: relative;
  height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.heroVideo {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.heroVideo video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.heroOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.7), rgba(45, 45, 45, 0.5));
  z-index: 2;
}

.heroContent {
  position: relative;
  z-index: 3;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 2rem;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.02em;
  margin-bottom: 1rem;
}

.highlight {
  color: #d4af37;
}

.heroSubtitle {
  font-size: 1.25rem;
  color: #cccccc;
  line-height: 1.6;
}

/* Container */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Factors Section */
.factorsSection {
  padding: 6rem 0;
  background: #f8f9fa;
}

.factorsHeader {
  text-align: center;
  margin-bottom: 4rem;
}

.factorsTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.factorsSubtitle {
  font-size: 1.25rem;
  color: #4a4a4a;
  line-height: 1.6;
}

.factorsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.factorCard {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  cursor: pointer;
}

.factorCard:hover {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
  border-color: #d4af37;
}

.factorIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.factorTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 0.75rem;
}

.factorDescription {
  font-size: 1rem;
  color: #4a4a4a;
  line-height: 1.6;
  margin: 0;
}

/* Quote Section */
.quoteSection {
  padding: 6rem 0;
  background: white;
}

.quoteGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.quoteTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.quoteDescription {
  font-size: 1.1rem;
  color: #4a4a4a;
  line-height: 1.7;
  margin-bottom: 2rem;
}

.quoteFeatures {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.quoteFeature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1rem;
  color: #1a1a1a;
}

.featureIcon {
  font-size: 1.2rem;
}

.quoteForm {
  background: #f8f9fa;
  border-radius: 20px;
  padding: 2.5rem;
  border: 1px solid #e9ecef;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 0.95rem;
}

.input,
.select,
.textarea {
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  background: white;
}

.input:focus,
.select:focus,
.textarea:focus {
  outline: none;
  border-color: #d4af37;
}

.serviceOptions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.serviceOption {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.95rem;
}

.serviceOption:hover {
  border-color: #d4af37;
}

.serviceOption.selected {
  background: #d4af37;
  color: #1a1a1a;
  border-color: #d4af37;
}

.serviceIcon {
  font-size: 1.1rem;
}

.submitButton {
  background: linear-gradient(135deg, #d4af37, #f4e4a6);
  color: #1a1a1a;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.submitButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .quoteGrid {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
}

@media (max-width: 768px) {
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .heroSubtitle {
    font-size: 1.1rem;
  }
  
  .factorsGrid {
    grid-template-columns: 1fr;
  }
  
  .formRow {
    grid-template-columns: 1fr;
  }
  
  .serviceOptions {
    grid-template-columns: 1fr;
  }
  
  .quoteForm {
    padding: 2rem;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 2rem;
  }
  
  .factorCard {
    padding: 1.5rem;
  }
  
  .quoteForm {
    padding: 1.5rem;
  }
  
  .container {
    padding: 0 1rem;
  }
}

/* Success Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.successModal {
  background: white;
  border-radius: 16px;
  padding: 3rem;
  max-width: 500px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modalIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.successModal h3 {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
}

.successModal p {
  color: #6b7280;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 2rem 0;
}

.modalActions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.dashboardLink {
  background: linear-gradient(135deg, #D4AF37, #B8860B);
  color: white;
  text-decoration: none;
  padding: 0.875rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.dashboardLink:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(212, 175, 55, 0.3);
}

.modalCloseButton {
  background: #6b7280;
  color: white;
  border: none;
  padding: 0.875rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modalCloseButton:hover {
  background: #4b5563;
  transform: translateY(-2px);
}

.vrContainer {
    position: relative;
    width: 100%;
    height: 100vh;
    background: black;
    overflow: hidden;
    font-family: 'Didot', serif;
  }
  
  .centerHeading {
    position: absolute;
    top: 45%;
    left: 50%;
    transform: translateX(-50%);
    font-size: 4rem;
    font-weight: bold;
    color: white;
    z-index: 10;
    pointer-events: none;
  }
  
  .videosWrapper {
    display: flex;
    width: 100%;
    height: 100%;
  }
  
  .half {
    position: relative;
    flex: 1;
    overflow: hidden;
    cursor: pointer;
  }
  
  .bgVideo {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .textOverlay {
    position: absolute;
    bottom: 15%;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    text-align: center;
    z-index: 2;
  }
  
  .textOverlay h2 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 10px;
  }
  
  .textOverlay button {
    border: none;
    border-bottom: 1px solid white;
    background: transparent;
    color: white;
    font-size: 1rem;
    cursor: pointer;
    padding-bottom: 4px;
  }
  
  .cleanLink {
    text-decoration: none;
    color: white;
    pointer-events: auto;
  }
  
  .cleanButton {
    text-decoration: none;
    color: white;
    font-size: 1rem;
    border-bottom: 1px solid white;
    padding-bottom: 4px;
    cursor: pointer;
  }

  
  .dimmedVideo {
    filter: brightness(50%);
    transition: filter 0.3s ease;
  }
  
  .half:hover .dimmedVideo {
    filter: brightness(100%);
  }
  
  @media (max-width: 768px) {
    .centerHeading {
      font-size: 2.5rem;
      top: 2%;
    }
  
    .videosWrapper {
      flex-direction: column;
    }
  
    .textOverlay {
      bottom: 20%;
      padding: 0 1rem;
    }
  
    .textOverlay h2 {
      font-size: 1.4rem;
    }
  
    .textOverlay button {
      font-size: 0.95rem;
    }
  }
  
.aboutPage {
  min-height: 100vh;
  background: white;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Hero Section */
.heroSection {
  padding: 8rem 0 6rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  text-align: center;
}

.heroContent {
  max-width: 900px;
  margin: 0 auto;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.highlight {
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Story Section */
.storySection {
  padding: 6rem 0;
  background: white;
}

.storyGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.sectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 2rem;
  letter-spacing: -0.02em;
}

.storyText {
  font-size: 1.25rem;
  color: #64748b;
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.storyText:last-child {
  margin-bottom: 0;
}

.storyVisual {
  display: flex;
  justify-content: center;
}

.visualCard {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 100%;
  max-width: 400px;
  border: 1px solid #e2e8f0;
}

.cardHeader {
  background: #f1f5f9;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.cardDots {
  display: flex;
  gap: 0.5rem;
}

.cardDots span {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #cbd5e1;
}

.cardDots span:first-child {
  background: #ef4444;
}

.cardDots span:nth-child(2) {
  background: #f59e0b;
}

.cardDots span:last-child {
  background: #10b981;
}

.cardContent {
  padding: 2rem;
  height: 200px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.designElement1 {
  height: 40px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 8px;
  opacity: 0.8;
}

.designElement2 {
  height: 30px;
  background: linear-gradient(135deg, #f093fb, #f5576c);
  border-radius: 8px;
  width: 70%;
  opacity: 0.6;
}

.designElement3 {
  height: 50px;
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  border-radius: 8px;
  width: 85%;
  opacity: 0.7;
}

/* Mission Section */
.missionSection {
  padding: 6rem 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  text-align: center;
}

.missionContent {
  max-width: 800px;
  margin: 0 auto;
}

.missionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  letter-spacing: -0.02em;
}

.missionText {
  font-size: 1.5rem;
  line-height: 1.6;
  font-style: italic;
  opacity: 0.95;
}

/* Values Section */
.valuesSection {
  padding: 6rem 0;
  background: #f8fafc;
}

.valuesHeader {
  text-align: center;
  margin-bottom: 4rem;
}

.valuesSubtitle {
  font-size: 1.25rem;
  color: #64748b;
  margin-top: 1rem;
}

.valuesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.valueCard {
  background: white;
  border-radius: 16px;
  padding: 2.5rem 2rem;
  text-align: center;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  cursor: pointer;
}

.valueCard:hover {
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.1);
  transform: translateY(-5px);
  border-color: #667eea;
}

.valueIcon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

.valueTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.valueDescription {
  font-size: 1rem;
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

/* CTA Section */
.ctaSection {
  padding: 6rem 0;
  background: white;
  text-align: center;
}

.ctaContent {
  max-width: 600px;
  margin: 0 auto;
}

.ctaTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.ctaDescription {
  font-size: 1.25rem;
  color: #64748b;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.ctaButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryButton {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.primaryButton:hover {
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  transform: translateY(-2px);
}

.secondaryButton {
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.secondaryButton:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .storyGrid {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
  
  .heroSection {
    padding: 6rem 0 4rem;
  }
  
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .sectionTitle {
    font-size: 2rem;
  }
  
  .storyText {
    font-size: 1.1rem;
  }
  
  .missionTitle {
    font-size: 2rem;
  }
  
  .missionText {
    font-size: 1.25rem;
  }
  
  .valuesGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .valueCard {
    padding: 2rem 1.5rem;
  }
  
  .ctaTitle {
    font-size: 2rem;
  }
  
  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .primaryButton,
  .secondaryButton {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 2rem;
  }
  
  .valueIcon {
    font-size: 2.5rem;
  }
}

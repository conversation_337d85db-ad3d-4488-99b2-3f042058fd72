/* Contact Section - Meta Style */
.section {
  position: relative;
  min-height: 100vh;
  padding: 8rem 0;
  overflow: hidden;
  background: #000;
  display: flex;
  align-items: center;
}

.videoContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.backgroundVideo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.3) contrast(1.2) saturate(1.1);
}

.videoOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.6) 50%,
    rgba(0, 0, 0, 0.4) 100%
  );
  z-index: 2;
}

.content {
  position: relative;
  z-index: 10;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
}

.header {
  text-align: center;
  margin-bottom: 4rem;
}

.sectionTitle {
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #ffffff 0%, #60a5fa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sectionSubtitle {
  font-size: clamp(1rem, 2vw, 1.25rem);
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.ctaContainer {
  text-align: center;
  margin-bottom: 4rem;
}

.ctaDescription {
  font-size: clamp(1rem, 2vw, 1.25rem);
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto 3rem;
}

.contactGrid {
  display: flex;
  justify-content: center;
  margin: 0 auto;
}

.contactCard {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  transition: all 0.4s ease;
  text-align: center;
  max-width: 400px;
  width: 100%;
}

.contactCard:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(96, 165, 250, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.contactCard h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
}

.contactCard p {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.primaryButton, .secondaryButton {
  display: inline-block;
  padding: 0.75rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.primaryButton {
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  color: white;
}

.primaryButton:hover {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(96, 165, 250, 0.3);
}

.secondaryButton {
  background: transparent;
  color: rgba(96, 165, 250, 0.9);
  border-color: rgba(96, 165, 250, 0.5);
}

.secondaryButton:hover {
  background: rgba(96, 165, 250, 0.1);
  color: white;
  border-color: rgba(96, 165, 250, 0.8);
  transform: translateY(-2px);
}



/* Responsive Design */
@media (max-width: 768px) {
  .section {
    padding: 4rem 0;
  }
  
  .content {
    padding: 0 1rem;
  }
  
  .header {
    margin-bottom: 2rem;
  }
  
  .ctaContainer {
    margin-bottom: 2rem;
  }



  .contactCard {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .contactCard {
    padding: 1.25rem;
  }
  
  .primaryButton, .secondaryButton {
    padding: 0.625rem 1.5rem;
    font-size: 0.9rem;
  }
}

.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background-color: #ffffff;
  padding: 2rem;
  border-radius: 1rem;
  width: 90%;
  max-width: 420px;
  color: #1f2937;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.3s ease;
}

.modal h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #111827;
}

.modal p {
  font-size: 0.95rem;
  color: #4b5563;
  margin-bottom: 1rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid #d1d5db;
  background-color: #f9fafb;
  color: #111827;
  margin-bottom: 1rem;
  font-size: 1rem;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #facc15;
  background-color: #fffbea;
  outline: none;
}

.modal-buttons {
  display: flex;
  justify-content: space-between;
  gap: 0.75rem;
  margin-top: 1rem;
}

.send-button,
.cancel-button {
  flex: 1;
  padding: 0.75rem 1.25rem;
  border-radius: 0.5rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  font-size: 0.95rem;
  transition: background-color 0.3s ease;
}

.send-button {
  background-color: #facc15;
  color: #111827;
}

.send-button:hover {
  background-color: #fbbf24;
}

.cancel-button {
  background-color: #f3f4f6;
  color: #1f2937;
}

.cancel-button:hover {
  background-color: #e5e7eb;
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

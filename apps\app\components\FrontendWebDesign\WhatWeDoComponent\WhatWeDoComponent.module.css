.whatWeDoSection {
  padding: 6rem 2rem;
  background: white;
  color: #1e293b;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
}

.introContent {
  text-align: center;
  margin-bottom: 5rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.introTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
  letter-spacing: -0.02em;
}

.introParagraph {
  font-size: 1.25rem;
  color: #64748b;
  line-height: 1.7;
  margin: 0;
}

.contentWrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 4rem;
  flex-wrap: wrap;
}

.left {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 600px;
}

.showcaseVideo {
  width: 100%;
  height: auto;
  border-radius: 20px;
  object-fit: cover;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.right {
  flex: 1;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 2rem;
}

.heading {
  font-size: 2rem;
  font-weight: 600;
  line-height: 1.4;
  color: #1e293b;
}

.ctaButton {
  padding: 1.2rem 3rem;
  border-radius: 12px;
  border: 2px solid #667eea;
  background: transparent;
  color: #667eea;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
  align-self: flex-start;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.ctaButton:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}
  
/* Responsive Design */
@media (max-width: 1024px) {
  .contentWrapper {
    flex-direction: column;
    gap: 3rem;
    text-align: center;
  }

  .left, .right {
    max-width: 100%;
  }

  .ctaButton {
    align-self: center;
  }
}

@media (max-width: 768px) {
  .whatWeDoSection {
    padding: 4rem 1rem;
  }

  .introTitle {
    font-size: 2rem;
  }

  .introParagraph {
    font-size: 1.1rem;
  }

  .heading {
    font-size: 1.75rem;
  }

  .contentWrapper {
    gap: 2rem;
  }
}

@media (max-width: 480px) {
  .introTitle {
    font-size: 1.75rem;
  }

  .introParagraph {
    font-size: 1rem;
  }

  .heading {
    font-size: 1.5rem;
  }

  .ctaButton {
    padding: 1rem 2rem;
    font-size: 1rem;
  }
}
  
  /* ✅ Make links look like plain text */
.menu-content a {
  text-decoration: none;
  color: inherit;
  transition: color 0.3s ease;
}

.menu-content a:hover {
  color: #888;
}

/* ✅ Hide bullets */
.menu-content li {
  list-style: none;
}

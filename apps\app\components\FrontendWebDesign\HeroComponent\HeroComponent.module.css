.heroSection {
  position: relative;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 8rem 2rem;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.gradientOrb1 {
  position: absolute;
  top: 10%;
  right: 10%;
  width: 300px;
  height: 300px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 50%;
  opacity: 0.1;
  filter: blur(40px);
  animation: float 6s ease-in-out infinite;
}

.gradientOrb2 {
  position: absolute;
  bottom: 20%;
  left: 5%;
  width: 200px;
  height: 200px;
  background: linear-gradient(45deg, #f093fb, #f5576c);
  border-radius: 50%;
  opacity: 0.1;
  filter: blur(30px);
  animation: float 8s ease-in-out infinite reverse;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.contentWrapper {
  position: relative;
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  z-index: 1;
}

.heroContent {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.heroHeadline {
  font-size: 3.5rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.highlight {
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.heroSubheadline {
  font-size: 1.25rem;
  color: #64748b;
  line-height: 1.6;
  max-width: 90%;
}

.ctaButtons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.primaryCta {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.primaryCta:hover {
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  transform: translateY(-2px);
}

.secondaryCta {
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.secondaryCta:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

.tertiaryCta {
  background: transparent;
  color: #64748b;
  border: 2px solid #e2e8f0;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.tertiaryCta:hover {
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-2px);
}

.heroVisual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.mockupContainer {
  position: relative;
  width: 100%;
  max-width: 500px;
}

.websitePreview {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
}

.browserBar {
  background: #f1f5f9;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.browserDots {
  display: flex;
  gap: 0.5rem;
}

.browserDots span {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #cbd5e1;
}

.browserDots span:first-child {
  background: #ef4444;
}

.browserDots span:nth-child(2) {
  background: #f59e0b;
}

.browserDots span:last-child {
  background: #10b981;
}

.websiteContent {
  padding: 2rem;
  height: 300px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.designElements {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 100%;
}

.designBlock1 {
  height: 60px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 8px;
  opacity: 0.8;
}

.designBlock2 {
  height: 40px;
  background: linear-gradient(135deg, #f093fb, #f5576c);
  border-radius: 8px;
  width: 70%;
  opacity: 0.6;
}

.designBlock3 {
  height: 80px;
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  border-radius: 8px;
  width: 85%;
  opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .contentWrapper {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .heroHeadline {
    font-size: 3rem;
  }

  .heroSubheadline {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .heroSection {
    padding: 4rem 1rem;
    min-height: auto;
  }

  .heroHeadline {
    font-size: 2.5rem;
  }

  .heroSubheadline {
    font-size: 1.1rem;
  }

  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }

  .primaryCta,
  .secondaryCta,
  .tertiaryCta {
    width: 100%;
    max-width: 300px;
  }

  .gradientOrb1,
  .gradientOrb2 {
    display: none;
  }
}

@media (max-width: 480px) {
  .heroHeadline {
    font-size: 2rem;
  }

  .heroSubheadline {
    font-size: 1rem;
  }

  .websitePreview {
    transform: none;
  }
}
  
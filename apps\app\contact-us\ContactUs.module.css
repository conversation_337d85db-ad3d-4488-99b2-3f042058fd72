/* Contact Us Page - Meta Style */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 6rem 2rem 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.content {
  max-width: 600px;
  width: 100%;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: clamp(2rem, 5vw, 3rem);
  font-weight: 700;
  color: #1c1e21;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.subtitle {
  font-size: 1.125rem;
  color: #65676b;
  line-height: 1.5;
  max-width: 500px;
  margin: 0 auto;
}

.formContainer {
  background: white;
  border-radius: 16px;
  padding: 2.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1c1e21;
  margin-bottom: 0.5rem;
  letter-spacing: 0.01em;
}

.input, .textarea {
  padding: 0.875rem 1rem;
  border: 2px solid #e4e6ea;
  border-radius: 8px;
  font-size: 1rem;
  color: #1c1e21;
  background: #f8f9fa;
  transition: all 0.2s ease;
  font-family: inherit;
}

.input:focus, .textarea:focus {
  outline: none;
  border-color: #0866ff;
  background: white;
  box-shadow: 0 0 0 3px rgba(8, 102, 255, 0.1);
}

.textarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.5;
}

.textarea::placeholder {
  color: #65676b;
}

.submitButton {
  background: linear-gradient(135deg, #0866ff, #0653d3);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 0.5rem;
}

.submitButton:hover {
  background: linear-gradient(135deg, #0653d3, #0542aa);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(8, 102, 255, 0.3);
}

.submitButton:active {
  transform: translateY(0);
}

.successMessage {
  text-align: center;
  padding: 2rem;
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  border-radius: 12px;
  border: 1px solid #b8dacc;
}

.successMessage h3 {
  color: #155724;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.successMessage p {
  color: #155724;
  font-size: 1rem;
  margin: 0;
}

.backLink {
  text-align: center;
}

.backButton {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #0866ff;
  text-decoration: none;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  background: rgba(8, 102, 255, 0.1);
}

.backButton:hover {
  background: rgba(8, 102, 255, 0.15);
  transform: translateX(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 4rem 1rem 2rem;
  }
  
  .formContainer {
    padding: 2rem;
  }
  
  .header {
    margin-bottom: 2rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 3rem 1rem 2rem;
  }
  
  .formContainer {
    padding: 1.5rem;
  }
  
  .form {
    gap: 1.25rem;
  }
}

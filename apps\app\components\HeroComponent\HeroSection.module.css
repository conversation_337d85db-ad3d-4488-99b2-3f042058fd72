/* Meta-Inspired Hero Section Styles */
.hero {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 64px; /* Account for fixed header */
}

.videoContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.heroVideo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.7) contrast(1.1);
}

.videoOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.2) 50%,
    rgba(0, 0, 0, 0.6) 100%
  );
  z-index: 2;
}

.heroContent {
  position: relative;
  z-index: 10;
  text-align: center;
  color: white;
  max-width: 1200px;
  padding: 0 2rem;
}

.titleContainer {
  margin-bottom: 2rem;
}

.mainTitle {
  font-size: clamp(3.5rem, 8vw, 8rem);
  font-weight: 700;
  line-height: 0.9;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #ffffff 0%, #e5e7eb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 40px rgba(255, 255, 255, 0.3);
}

.subtitle {
  font-size: clamp(1.5rem, 4vw, 3rem);
  font-weight: 300;
  line-height: 1.2;
  margin-bottom: 0;
  letter-spacing: 0.01em;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
}

.description {
  font-size: clamp(1rem, 2vw, 1.25rem);
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  max-width: 800px;
  margin: 2rem auto 3rem;
  font-weight: 300;
  text-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
}



/* Floating Elements */
.floatingElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5;
}

.floatingCircle1 {
  position: absolute;
  top: 20%;
  right: 15%;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.3), rgba(59, 130, 246, 0.2));
  border-radius: 50%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.floatingCircle2 {
  position: absolute;
  top: 60%;
  left: 10%;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.3), rgba(251, 191, 36, 0.2));
  border-radius: 50%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.floatingCircle3 {
  position: absolute;
  top: 30%;
  left: 20%;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(236, 72, 153, 0.2), rgba(219, 39, 119, 0.1));
  border-radius: 50%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .heroContent {
    padding: 0 1rem;
  }

  .description {
    margin: 1.5rem auto 2rem;
  }

  .floatingCircle1 {
    width: 40px;
    height: 40px;
    top: 25%;
    right: 10%;
  }

  .floatingCircle2 {
    width: 30px;
    height: 30px;
    top: 65%;
    left: 5%;
  }

  .floatingCircle3 {
    width: 50px;
    height: 50px;
    top: 35%;
    left: 15%;
  }
}

@media (max-width: 480px) {
  .titleContainer {
    margin-bottom: 1.5rem;
  }

  .description {
    margin: 1rem auto 1.5rem;
  }

  .floatingElements {
    display: none; /* Hide on very small screens to avoid clutter */
  }
}

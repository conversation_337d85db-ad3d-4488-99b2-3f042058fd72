.influencerSection {
    padding: 6rem 2rem;
    background-color: #ffffff;
    color: #111;
    text-align: center;
    box-sizing: border-box;
    min-height: 100vh;
    font-family: 'Helvetica Neue', sans-serif;
  }
  
  .heading {
    font-size: 2.5rem;
    font-weight: 500;
    margin-bottom: 4rem;
    letter-spacing: 2px;
  }
  
  .subtext {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 3rem;
    letter-spacing: 1px;
  }
  
  /* Grid Layout */
  .grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2.5rem;
    width: 100%;
    margin: 0 auto;
  }
  
  /* Individual Card */
  .card {
    background-color: #000; /* fallback */
    border-radius: 1.5rem;
    overflow: hidden;
    padding: 0;
    width: 320px;
    height: 400px;
    position: relative;
    flex-shrink: 0;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
    transition: transform 0.4s ease, box-shadow 0.4s ease;
  }
  
  .card:hover {
    transform: translateY(-10px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
  }
  
  /* Video Background */
  .bgVideo {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 0;
    opacity: 0.8; /* little dark for text visibility */
  }
  
  /* Content overlay center fix */
  .contentOverlay {
    position: absolute;
    inset: 0;
    z-index: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    background: rgba(0, 0, 0, 0.35); /* subtle overlay */
    text-align: center;
  }
  
  .title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    max-width: 80%;
    line-height: 1.3;
    color: #fff;
    word-break: break-word;
  }
  
  .description {
    font-size: 1rem;
    font-weight: 400;
    color: #ddd;
    max-width: 80%;
    line-height: 1.5;
    word-break: break-word;
  }
  
  /* 📱 Mobile Styles */
  @media (max-width: 768px) {
    .grid {
      flex-direction: column;
      align-items: center;
    }
  
    .card {
      width: 90%;
      max-width: 350px;
    }
  
    .heading {
      font-size: 2rem;
    }
  
    .title {
      font-size: 1.2rem;
    }
  
    .description {
      font-size: 0.9rem;
    }
  }
  
/* signup.css */

.signup-container {
  background-color: #ffffff;
  color: #1f2937;
  min-height: 100vh;
  padding: 2rem 1.5rem;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.signup-heading {
  text-align: center;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2rem;
}

.signup-form {
  max-width: 600px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.form-input {
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  font-size: 1rem;
  color: #111827;
}

.form-input:focus {
  border-color: #facc15;
  outline: none;
  background-color: #fffbea;
}

.submit-btn {
  padding: 0.85rem 1.25rem;
  background-color: #facc15;
  color: #111827;
  font-weight: 600;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.submit-btn:hover {
  background-color: #fbbf24;
}

@media (max-width: 768px) {
  .signup-heading {
    font-size: 1.6rem;
  }

  .signup-form {
    padding: 0 1rem;
  }
}

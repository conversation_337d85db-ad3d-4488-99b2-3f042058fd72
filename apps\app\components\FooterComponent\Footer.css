.footer {
  background-color: #fff;
  border-top: 1px solid #e5e7eb;
  width: 100%;
  padding: 4rem 2rem;
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.03);
}

.footer-content {
  max-width: 1280px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 2rem;
}

.footer-left {
  flex: 1 1 55%;
}

.footer-heading {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #1f2937;
}

.footer-heading .highlight {
  color: #6366f1;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.name-fields {
  display: flex;
  gap: 1rem;
}

.name-fields input {
  flex: 1;
}

.contact-form input,
.contact-form textarea {
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 1rem;
  background-color: #fff;
  color: #1f2937;
}

.contact-form textarea {
  resize: none;
  height: 100px;
}

.contact-form button {
  width: fit-content;
  padding: 0.75rem 1.5rem;
  background-color: #6366f1;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
}

.contact-form button:hover {
  background-color: #4f46e5;
}

/* Right side */
.footer-right {
  flex: 1 1 35%;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.footer-right h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.75rem;
}

.footer-links {
  list-style: none;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-links a {
  text-decoration: none;
  color: #4b5563;
  font-weight: 500;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #111827;
  font-weight: 600;
}

.contact-info p {
  color: #4b5563;
  margin: 0.25rem 0;
  font-size: 0.95rem;
}

/* 📱 Mobile Responsiveness */
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 3rem;
  }

  .name-fields {
    flex-direction: column; /* 👈 stack first name & last name vertically */
  }

  .contact-form button {
    width: 100%; /* 👈 button full width for mobile look */
    text-align: center;
  }
}

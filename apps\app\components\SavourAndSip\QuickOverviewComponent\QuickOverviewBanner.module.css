.overviewSection {
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  position: relative;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
}

.servicesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.serviceCard {
  background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
  border: 1px solid #333;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.serviceCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #d4af37, #f4e4a6);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.serviceCard:hover::before {
  transform: scaleX(1);
}

.serviceCard:hover {
  border-color: #d4af37;
  box-shadow: 0 10px 30px rgba(212, 175, 55, 0.2);
  transform: translateY(-5px);
}

.serviceIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.serviceTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.serviceDescription {
  font-size: 1rem;
  color: #cccccc;
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .overviewSection {
    padding: 3rem 1rem;
  }
  
  .servicesGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .serviceCard {
    padding: 1.5rem;
  }
  
  .serviceIcon {
    font-size: 2.5rem;
  }
  
  .serviceTitle {
    font-size: 1.1rem;
  }
  
  .serviceDescription {
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .serviceCard {
    padding: 1.25rem;
  }
  
  .serviceIcon {
    font-size: 2rem;
  }
}

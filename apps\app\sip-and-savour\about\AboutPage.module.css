.aboutPage {
  min-height: 100vh;
  background: #ffffff;
}

/* Navigation styles removed - using SharedHeader component */

/* Hero Section */
.heroSection {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.heroVideo {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.heroVideo video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.heroOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.6), rgba(45, 45, 45, 0.4));
  z-index: 2;
}

.heroContent {
  position: relative;
  z-index: 3;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 2rem;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.highlight {
  color: #d4af37;
}

/* Container */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Story Section */
.storySection {
  padding: 6rem 0;
  background: #ffffff;
}

.storyGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.sectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 2rem;
  letter-spacing: -0.02em;
}

.storyText {
  font-size: 1.25rem;
  color: #4a4a4a;
  line-height: 1.7;
  margin: 0;
}

.storyImage {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Mission Section */
.missionSection {
  padding: 6rem 0;
  background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
  color: white;
  text-align: center;
}

.missionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  letter-spacing: -0.02em;
  color: #d4af37;
}

.missionText {
  font-size: 1.5rem;
  line-height: 1.6;
  font-style: italic;
  max-width: 800px;
  margin: 0 auto;
  opacity: 0.95;
}

/* Values Section */
.valuesSection {
  padding: 6rem 0;
  background: #f8f9fa;
}

.valuesHeader {
  text-align: center;
  margin-bottom: 4rem;
}

.valuesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.valueCard {
  background: white;
  border-radius: 16px;
  padding: 2.5rem 2rem;
  text-align: center;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  cursor: pointer;
}

.valueCard:hover {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
  border-color: #d4af37;
}

.valueIcon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

.valueTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.valueDescription {
  font-size: 1rem;
  color: #4a4a4a;
  line-height: 1.6;
  margin: 0;
}

/* Team Section */
.teamSection {
  padding: 6rem 0;
  background: white;
}

.teamHeader {
  text-align: center;
  margin-bottom: 4rem;
}

.teamSubtitle {
  font-size: 1.25rem;
  color: #4a4a4a;
  margin-top: 1rem;
}

.teamGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.teamCard {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  cursor: pointer;
}

.teamCard:hover {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.memberImage {
  height: 250px;
  overflow: hidden;
}

.memberImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.teamCard:hover .memberImage img {
  transform: scale(1.1);
}

.memberInfo {
  padding: 2rem;
  text-align: center;
}

.memberName {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 0.5rem;
}

.memberRole {
  font-size: 1rem;
  color: #d4af37;
  font-weight: 500;
  margin-bottom: 1rem;
}

.memberBio {
  font-size: 0.95rem;
  color: #4a4a4a;
  line-height: 1.6;
  margin: 0;
}

/* CTA Section */
.ctaSection {
  padding: 6rem 0;
  background: linear-gradient(135deg, #d4af37, #f4e4a6);
  text-align: center;
}

.ctaContent {
  max-width: 600px;
  margin: 0 auto;
}

.ctaTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.ctaDescription {
  font-size: 1.25rem;
  color: #2d2d2d;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.ctaButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryButton {
  background: #1a1a1a;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.primaryButton:hover {
  background: #2d2d2d;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(26, 26, 26, 0.3);
}

.secondaryButton {
  background: transparent;
  color: #1a1a1a;
  border: 2px solid #1a1a1a;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.secondaryButton:hover {
  background: #1a1a1a;
  color: white;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .storyGrid {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .sectionTitle {
    font-size: 2rem;
  }
  
  .storyText {
    font-size: 1.1rem;
  }
  
  .missionTitle {
    font-size: 2rem;
  }
  
  .missionText {
    font-size: 1.25rem;
  }
  
  .valuesGrid,
  .teamGrid {
    grid-template-columns: 1fr;
  }
  
  .ctaTitle {
    font-size: 2rem;
  }
  
  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .primaryButton,
  .secondaryButton {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 2rem;
  }
  
  .container {
    padding: 0 1rem;
  }
}

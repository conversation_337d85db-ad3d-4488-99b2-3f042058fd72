.pollingSection {
  background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
  color: white;
  min-height: 100vh;
  padding: 2rem 0;
  font-family: 'Helvetica Neue', sans-serif;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #ffffff, #cccccc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.2rem;
  color: #cccccc;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.pollsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.pollCard {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.pollCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(255, 255, 255, 0.1);
}

.pollHeader {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.pollTitle {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 0.8rem;
  color: white;
}

.pollMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: #999999;
}

.voteCount {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
}

.endDate {
  color: #ffcc00;
}

.pollQuestion {
  margin-bottom: 1.5rem;
}

.pollQuestion p {
  font-size: 1.1rem;
  color: #eeeeee;
  line-height: 1.5;
}

.optionsContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.optionContainer {
  width: 100%;
}

.voteOption {
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.voteOption:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateX(5px);
}

.resultOption {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
}

.optionText {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
  font-size: 1rem;
  color: #eeeeee;
}

.percentage {
  font-weight: 600;
  color: #ffffff;
}

.progressBar {
  background: rgba(255, 255, 255, 0.1);
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #ffffff, #cccccc);
  border-radius: 4px;
  transition: width 0.8s ease;
}

.resultOption .voteCount {
  font-size: 0.85rem;
  color: #999999;
  background: none;
  padding: 0;
}

.thankYou {
  margin-top: 1.5rem;
  padding: 1rem;
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 6px;
  text-align: center;
}

.thankYou p {
  color: #4CAF50;
  margin: 0;
  font-size: 0.95rem;
}

.infoSection {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.infoCard {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1.5rem;
}

.infoCard h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: white;
}

.infoCard p {
  color: #cccccc;
  line-height: 1.6;
  margin: 0;
}

.ctaSection {
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 3rem 2rem;
}

.ctaSection h2 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: white;
}

.ctaSection p {
  font-size: 1.1rem;
  color: #cccccc;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.ctaButton {
  background: linear-gradient(45deg, #ffffff, #cccccc);
  color: black;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ctaButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.loadingSpinner {
  text-align: center;
  padding: 4rem 2rem;
  font-size: 1.2rem;
  color: #cccccc;
}

.noPolls {
  text-align: center;
  padding: 4rem 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.noPolls h2 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: white;
}

.noPolls p {
  font-size: 1.1rem;
  color: #cccccc;
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .title {
    font-size: 2rem;
  }

  .pollsGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .pollCard {
    padding: 1.5rem;
  }

  .pollMeta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .infoSection {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .ctaSection {
    padding: 2rem 1.5rem;
  }

  .ctaSection h2 {
    font-size: 1.5rem;
  }
}

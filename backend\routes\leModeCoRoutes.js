const express = require('express');
const router = express.Router();
const LeModeCoService = require('../services/leModeCoService');
const AuthService = require('../services/authService');
const EmailService = require('../services/emailService');

// Public Routes (for customers)

// Get all active packages
router.get('/packages', async (req, res) => {
  try {
    const packages = await LeModeCoService.getAllPackages(false);
    
    res.json({
      success: true,
      packages
    });
  } catch (error) {
    console.error('Error fetching packages:', error);
    res.status(500).json({
      error: 'Failed to fetch packages',
      details: error.message
    });
  }
});

// Create subscription
router.post('/subscribe', async (req, res) => {
  try {
    const { email, fullName, phone, zipCode, address, packageId } = req.body;

    // Validate required fields
    if (!email || !fullName || !phone || !zipCode || !address || !packageId) {
      return res.status(400).json({
        error: 'All fields are required'
      });
    }

    // Check if customer already has an active subscription
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    
    const existingSubscription = await prisma.customerSubscription.findFirst({
      where: {
        email,
        status: { in: ['PENDING', 'ACTIVE'] }
      }
    });

    if (existingSubscription) {
      return res.status(400).json({
        error: 'You already have an active or pending subscription'
      });
    }

    const subscription = await LeModeCoService.createSubscription({
      email,
      fullName,
      phone,
      zipCode,
      address,
      packageId
    });

    res.status(201).json({
      success: true,
      message: 'Subscription created successfully',
      subscription
    });
  } catch (error) {
    console.error('Error creating subscription:', error);
    res.status(500).json({
      error: 'Failed to create subscription',
      details: error.message
    });
  }
});

// Create payment intent
router.post('/create-payment-intent', async (req, res) => {
  try {
    const { subscriptionId } = req.body;

    if (!subscriptionId) {
      return res.status(400).json({
        error: 'Subscription ID is required'
      });
    }

    const paymentData = await LeModeCoService.createStripePaymentIntent(subscriptionId);

    res.json({
      success: true,
      ...paymentData
    });
  } catch (error) {
    console.error('Error creating payment intent:', error);
    res.status(500).json({
      error: 'Failed to create payment intent',
      details: error.message
    });
  }
});

// Confirm payment
router.post('/confirm-payment', async (req, res) => {
  try {
    const { paymentIntentId } = req.body;

    if (!paymentIntentId) {
      return res.status(400).json({
        error: 'Payment intent ID is required'
      });
    }

    const subscription = await LeModeCoService.confirmPayment(paymentIntentId);

    res.json({
      success: true,
      message: 'Payment confirmed successfully',
      subscription
    });
  } catch (error) {
    console.error('Error confirming payment:', error);
    res.status(500).json({
      error: 'Failed to confirm payment',
      details: error.message
    });
  }
});

// Admin Routes (require authentication)

// Get all packages (including inactive)
router.get('/admin/packages', AuthService.requireAuth, async (req, res) => {
  try {
    const packages = await LeModeCoService.getAllPackages(true);
    
    res.json({
      success: true,
      packages
    });
  } catch (error) {
    console.error('Error fetching packages:', error);
    res.status(500).json({
      error: 'Failed to fetch packages',
      details: error.message
    });
  }
});

// Create package
router.post('/admin/packages', AuthService.requireAuth, async (req, res) => {
  try {
    const packageData = req.body;
    const subscriptionPackage = await LeModeCoService.createPackage(packageData);

    res.status(201).json({
      success: true,
      message: 'Package created successfully',
      package: subscriptionPackage
    });
  } catch (error) {
    console.error('Error creating package:', error);
    res.status(500).json({
      error: 'Failed to create package',
      details: error.message
    });
  }
});

// Update package
router.put('/admin/packages/:packageId', AuthService.requireAuth, async (req, res) => {
  try {
    const { packageId } = req.params;
    const updateData = req.body;
    
    const subscriptionPackage = await LeModeCoService.updatePackage(packageId, updateData);

    res.json({
      success: true,
      message: 'Package updated successfully',
      package: subscriptionPackage
    });
  } catch (error) {
    console.error('Error updating package:', error);
    res.status(500).json({
      error: 'Failed to update package',
      details: error.message
    });
  }
});

// Delete package
router.delete('/admin/packages/:packageId', AuthService.requireAuth, async (req, res) => {
  try {
    const { packageId } = req.params;
    
    const result = await LeModeCoService.deletePackage(packageId);
    
    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    console.error('Error deleting package:', error);
    res.status(500).json({
      error: 'Failed to delete package',
      details: error.message
    });
  }
});

// Get all subscriptions
router.get('/admin/subscriptions', AuthService.requireAuth, async (req, res) => {
  try {
    const filters = req.query;
    const result = await LeModeCoService.getAllSubscriptions(filters);
    
    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    console.error('Error fetching subscriptions:', error);
    res.status(500).json({
      error: 'Failed to fetch subscriptions',
      details: error.message
    });
  }
});

// Get orders for a subscription
router.get('/admin/subscriptions/:subscriptionId/orders', AuthService.requireAuth, async (req, res) => {
  try {
    const { subscriptionId } = req.params;
    const orders = await LeModeCoService.getOrdersBySubscription(subscriptionId);
    
    res.json({
      success: true,
      orders
    });
  } catch (error) {
    console.error('Error fetching orders:', error);
    res.status(500).json({
      error: 'Failed to fetch orders',
      details: error.message
    });
  }
});

// Add item to order
router.post('/admin/orders/:orderId/items', AuthService.requireAuth, async (req, res) => {
  try {
    const { orderId } = req.params;
    const itemData = req.body;
    
    const item = await LeModeCoService.addItemToOrder(orderId, itemData);
    
    res.status(201).json({
      success: true,
      message: 'Item added successfully',
      item
    });
  } catch (error) {
    console.error('Error adding item:', error);
    res.status(500).json({
      error: 'Failed to add item',
      details: error.message
    });
  }
});

// Remove item from order
router.delete('/admin/orders/items/:itemId', AuthService.requireAuth, async (req, res) => {
  try {
    const { itemId } = req.params;
    
    const result = await LeModeCoService.removeItemFromOrder(itemId);
    
    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    console.error('Error removing item:', error);
    res.status(500).json({
      error: 'Failed to remove item',
      details: error.message
    });
  }
});

// Complete order
router.put('/admin/orders/:orderId/complete', AuthService.requireAuth, async (req, res) => {
  try {
    const { orderId } = req.params;
    
    const order = await LeModeCoService.completeOrder(orderId);
    
    res.json({
      success: true,
      message: 'Order completed successfully',
      order
    });
  } catch (error) {
    console.error('Error completing order:', error);
    res.status(500).json({
      error: 'Failed to complete order',
      details: error.message
    });
  }
});

// Notify customer
router.post('/admin/orders/:orderId/notify', AuthService.requireAuth, async (req, res) => {
  try {
    const { orderId } = req.params;
    
    const notificationData = await LeModeCoService.notifyCustomer(orderId);
    
    // Send email notification
    const emailResult = await EmailService.sendLeModeCoOrderNotification(notificationData.emailData);
    
    res.json({
      success: true,
      message: 'Customer notified successfully',
      emailResult
    });
  } catch (error) {
    console.error('Error notifying customer:', error);
    res.status(500).json({
      error: 'Failed to notify customer',
      details: error.message
    });
  }
});

// Get dashboard statistics
router.get('/admin/dashboard-stats', AuthService.requireAuth, async (req, res) => {
  try {
    const stats = await LeModeCoService.getDashboardStats();
    
    res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    res.status(500).json({
      error: 'Failed to fetch dashboard stats',
      details: error.message
    });
  }
});

module.exports = router;

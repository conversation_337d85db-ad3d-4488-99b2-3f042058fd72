.featureSection {
  background-color: #0b0f19;
  color: white;
  padding: 6rem 2rem;
  text-align: center;
}

.sectionHeading {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 4rem;
}

.serviceBlock {
  margin-bottom: 6rem;
}

.serviceTitle {
  font-size: 2rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.video {
  width: 80%;
  max-width: 960px;
  margin: 0 auto 2.5rem;
  border-radius: 1rem;
  box-shadow: 0 0 30px rgba(255, 255, 255, 0.05);
  transition: transform 0.6s ease-in-out;
}

.zoomIn {
  transform: scale(1.06);
}

.subsidiaries {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
}

.subCard {
  background-color: #171c24;
  padding: 2rem;
  border-radius: 1rem;
  width: 280px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.subCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 28px rgba(255, 255, 255, 0.05);
}

.subCard h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.subCard p {
  font-size: 0.95rem;
  color: #cbd5e1;
  margin-bottom: 1.25rem;
}

.subButton {
  display: inline-block;
  padding: 0.6rem 1.2rem;
  background-color: white;
  color: black;
  border-radius: 0.375rem;
  font-weight: 600;
  font-size: 0.95rem;
  text-decoration: none;
  transition: background 0.3s ease;
}

.subButton:hover {
  background-color: #f3f4f6;
}

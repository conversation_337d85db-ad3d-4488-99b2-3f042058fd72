.cuberto-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: white;
  color: black;
  position: relative;
  z-index: 50;
  font-family: 'Segoe UI', sans-serif;
  box-sizing: border-box;
}

.menu-button {
  background: none;
  color: black;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: black;
}

.hamburger {
  font-size: 1.3rem;
}

.menu-overlay {
  position: fixed;
  right: 0;
  top: 0;
  width: 50vw;
  max-width: 400px;
  height: 100vh;
  background: white;
  color: black;
  padding: 3rem 2rem;
  display: flex;
  flex-direction: column;
  z-index: 100;
  animation: slideIn 0.5s ease forwards;
  box-sizing: border-box;
  overflow-y: auto;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0%);
  }
}

.close-button {
  font-size: 2rem;
  border: none;
  background: none;
  align-self: flex-end;
  cursor: pointer;
}

.menu-content {
  display: flex;
  flex: 1;
  margin-top: 2rem;
  justify-content: space-between;
  gap: 2rem;
}

.menu-left ul,
.menu-right ul {
  list-style: none;
  padding: 0;
  margin: 1rem 0;
}

.menu-left li,
.menu-right li {
  margin-bottom: 0.75rem;
  font-size: 1rem;
  transition: transform 0.3s ease;
  cursor: pointer;
}

.menu-right li:hover {
  transform: scale(1.2);
}

.menu-heading {
  font-size: 0.85rem;
  font-weight: 500;
  color: gray;
  margin-bottom: 0.5rem;
}

.contact-block {
  margin-top: 2rem;
  font-size: 0.9rem;
}

/* ✅ Make links look like plain text */
.menu-content a {
  text-decoration: none;
  color: inherit;
  transition: color 0.3s ease;
}

.menu-content a:hover {
  color: #888;
}

/* ✅ Hide bullets */
.menu-content li {
  list-style: none;
}

.cursor-circle {
  position: fixed;
  width: 50px;
  height: 50px;
  border-radius: 999px;
  background: black;
  pointer-events: none;
  mix-blend-mode: difference;
  z-index: 200;
  transition: all 0.1s ease;
}

/* ✅ Mobile Responsive */
@media (max-width: 768px) {
  .menu-overlay {
    width: 100vw;
    max-width: 100vw;
    padding: 2rem 1.5rem;
  }

  .menu-content {
    flex-direction: column;
  }

  .menu-left,
  .menu-right {
    width: 100%;
  }

  .cursor-circle {
    display: none;
  }
}

.subscription-section {
  padding: 4rem 2rem;
  background-color: #ffffff;
  color: #111827;
  text-align: center;
  box-sizing: border-box;
  margin-top: 4rem;
}

.subscription-heading {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 2rem;
  color: #111827;
}

.subscription-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  padding: 1rem 0;
}

.subscription-card {
  background-color: #f9fafb;
  border-radius: 1rem;
  padding: 2rem;
  width: 320px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
  transition: transform 0.3s ease, border 0.3s ease;
  box-sizing: border-box;
  position: relative;
  cursor: pointer;
  border: 2px solid transparent;
}

.subscription-card:hover {
  transform: translateY(-6px);
}

.popular {
  border-color: #facc15;
}

.selected {
  border-color: #10b981;
}

.badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #facc15;
  color: #111827;
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  border-radius: 999px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.subscription-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #111827;
}

.subscription-price {
  color: #6b7280;
  margin-bottom: 1rem;
}

.subscription-features {
  list-style: none;
  padding: 0;
  margin: 1rem 0;
  color: #374151;
  text-align: left;
}

.subscription-features li {
  margin-bottom: 0.5rem;
}

.subscription-tooltip {
  font-size: 0.85rem;
  color: #9ca3af;
  margin-top: 0.5rem;
  font-style: italic;
}

/* 📱 Responsive */
@media (max-width: 768px) {
  .subscription-card {
    width: 100%;
    max-width: 360px;
  }

  .subscription-grid {
    flex-direction: column;
    align-items: center;
  }
}

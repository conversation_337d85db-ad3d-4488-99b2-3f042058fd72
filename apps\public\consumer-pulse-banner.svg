<svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e5e7eb;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="400" fill="url(#bgGradient)"/>
  
  <!-- Decorative elements -->
  <circle cx="100" cy="100" r="50" fill="rgba(255,255,255,0.1)"/>
  <circle cx="700" cy="300" r="80" fill="rgba(255,255,255,0.05)"/>
  <circle cx="650" cy="80" r="30" fill="rgba(255,255,255,0.1)"/>
  
  <!-- News icons -->
  <rect x="50" y="320" width="60" height="40" rx="5" fill="rgba(255,255,255,0.2)"/>
  <rect x="120" y="330" width="40" height="30" rx="3" fill="rgba(255,255,255,0.15)"/>
  <rect x="170" y="325" width="50" height="35" rx="4" fill="rgba(255,255,255,0.1)"/>
  
  <!-- Main text -->
  <text x="400" y="180" font-family="Arial, sans-serif" font-size="48" font-weight="bold" text-anchor="middle" fill="url(#textGradient)">
    Consumer Pulse
  </text>
  
  <!-- Subtitle -->
  <text x="400" y="220" font-family="Arial, sans-serif" font-size="20" text-anchor="middle" fill="rgba(255,255,255,0.8)">
    Real-Time News &amp; Market Intelligence
  </text>
  
  <!-- Bottom accent -->
  <rect x="0" y="380" width="800" height="20" fill="rgba(255,255,255,0.1)"/>
  
  <!-- Pulse animation effect -->
  <circle cx="400" cy="280" r="15" fill="rgba(255,255,255,0.3)">
    <animate attributeName="r" values="15;25;15" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.3;0.1;0.3" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Data visualization elements -->
  <g transform="translate(600, 250)">
    <rect x="0" y="20" width="8" height="30" fill="rgba(255,255,255,0.4)"/>
    <rect x="12" y="10" width="8" height="40" fill="rgba(255,255,255,0.5)"/>
    <rect x="24" y="15" width="8" height="35" fill="rgba(255,255,255,0.3)"/>
    <rect x="36" y="5" width="8" height="45" fill="rgba(255,255,255,0.6)"/>
  </g>
</svg>

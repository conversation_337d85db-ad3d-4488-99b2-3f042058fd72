.portfolioPage {
  min-height: 100vh;
  background: white;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Hero Section */
.heroSection {
  padding: 8rem 0 6rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  text-align: center;
}

.heroContent {
  max-width: 800px;
  margin: 0 auto;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
  letter-spacing: -0.02em;
  margin-bottom: 1rem;
}

.highlight {
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.heroSubtitle {
  font-size: 1.25rem;
  color: #64748b;
  line-height: 1.6;
}

/* Filter Section */
.filterSection {
  padding: 3rem 0;
  background: white;
  border-bottom: 1px solid #e2e8f0;
}

.filterButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.filterButton {
  background: transparent;
  color: #64748b;
  border: 2px solid #e2e8f0;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filterButton:hover,
.filterButton.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

/* Portfolio Section */
.portfolioSection {
  padding: 6rem 0;
  background: white;
}

.portfolioGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 3rem;
}

.projectCard {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.projectCard:hover {
  border-color: #667eea;
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.1);
  transform: translateY(-5px);
}

.projectImage {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.imagePlaceholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.placeholderText {
  font-size: 1.1rem;
  font-weight: 600;
  color: #64748b;
  text-align: center;
  padding: 1rem;
}

.projectOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(102, 126, 234, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.projectCard:hover .projectOverlay {
  opacity: 1;
}

.viewButton {
  background: white;
  color: #667eea;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.viewButton:hover {
  background: #f8fafc;
  transform: scale(1.05);
}

.projectContent {
  padding: 2rem;
}

.projectHeader {
  margin-bottom: 1rem;
}

.projectTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.projectIndustry {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.projectDescription {
  font-size: 1rem;
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.projectFeatures {
  margin-top: 1.5rem;
}

.featuresTitle {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.75rem;
}

.featuresTags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.featureTag {
  background: #f1f5f9;
  color: #64748b;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid #e2e8f0;
}

/* CTA Section */
.ctaSection {
  padding: 6rem 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  text-align: center;
}

.ctaContent {
  max-width: 600px;
  margin: 0 auto;
}

.ctaTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.ctaDescription {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  line-height: 1.6;
  opacity: 0.95;
}

.ctaButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryButton {
  background: white;
  color: #667eea;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.primaryButton:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.secondaryButton {
  background: transparent;
  color: white;
  border: 2px solid white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.secondaryButton:hover {
  background: white;
  color: #667eea;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .portfolioGrid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
  
  .heroSection {
    padding: 6rem 0 4rem;
  }
  
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .filterButtons {
    gap: 0.5rem;
  }
  
  .filterButton {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
  
  .portfolioGrid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .projectContent {
    padding: 1.5rem;
  }
  
  .ctaTitle {
    font-size: 2rem;
  }
  
  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .primaryButton,
  .secondaryButton {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 2rem;
  }
  
  .projectImage {
    height: 200px;
  }
  
  .projectContent {
    padding: 1.25rem;
  }
  
  .filterButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .filterButton {
    width: 100%;
    max-width: 200px;
  }
}

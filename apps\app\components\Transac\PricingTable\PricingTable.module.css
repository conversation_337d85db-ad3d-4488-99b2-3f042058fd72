/* Pricing Section */
.pricingSection {
  padding: 4rem 2rem;
  background-color: #f9fafb;
  color: #1f2937;
  text-align: center;
  box-sizing: border-box;
  min-height: 100vh;  /* Ensures the section height fills the screen */
}

.heading {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 3rem;
  color: #111827;
}

.grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;  /* Center the cards */
  gap: 2rem;
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
}

.card {
  background-color: #ffffff;
  border-radius: 1rem;
  padding: 1rem;
  width: 300px;  /* Fixed width for cards */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  flex-shrink: 0;  /* Prevent cards from shrinking */
}

.card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.08);
}

.popular {
  border: 2px solid #facc15;
}

.badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #facc15;
  color: #1f2937;
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  border-radius: 999px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.price {
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: #4b5563;
}

.features {
  list-style: none;
  padding: 0;
  margin: 1rem 0;
  text-align: left;
  color: #374151;
}

.features li {
  margin-bottom: 0.5rem;
}

.tooltip {
  font-size: 0.85rem;
  color: #6b7280;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
  font-style: italic;
}

.cta {
  margin-top: auto;
  background: transparent;
  border: 1px solid #111827;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  color: #111827;
  text-decoration: none;
  transition: all 0.3s ease;
}

.cta:hover {
  background-color: #111827;
  color: #fff;
}

/* 📱 Mobile Styles */
@media (max-width: 768px) {
  .grid {
    flex-direction: row;  /* Arrange cards horizontally on mobile */
    gap: 2rem;
    overflow-x: scroll;  /* Allow horizontal scrolling */
    padding: 1rem;
    justify-content: flex-start;  /* Align cards to the left */
    width: 100%;
  }

  .card {
    width: 250px;  /* Adjust card width on mobile */
    flex-shrink: 0;  /* Prevent shrinking */
  }

  .heading {
    font-size: 1.6rem;
  }

  .ctaButton {
    font-size: 1rem;
  }

  /* Ensure the section fills the entire viewport height */
  .pricingSection {
    min-height: 100vh;
  }
}
